# Required imports remain the same, add queue
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
import subprocess
import os
import platform
import shutil
import threading
import signal
import sys
import re
import traceback
import queue # Import the queue module

class DviSvgConverterApp:
    def __init__(self, master):
        self.master = master
        master.title("DVI/EPS/PDF to SVG Converter")
        master.geometry("600x470")

        signal.signal(signal.SIGINT, self.signal_handler)

        self.input_files = []
        self.output_folder = tk.StringVar()
        self.allowed_extensions = ('.dvi', '.eps', '.pdf')
        self.conversion_running = False
        self.conversion_thread = None
        self.dvisvgm_process = None

        # --- !!! Create the Queue for thread communication !!! ---
        self.gui_queue = queue.Queue()

        # --- Check for dvisvgm ---
        # (This part remains the same)
        if not self.is_dvisvgm_available():
            messagebox.showerror("Error",
                                 "'dvisvgm' command not found.\nPlease install it (usually part of TeX Live/MiKTeX) and ensure it's in your system's PATH.")
            master.destroy()
            return

        # --- GUI Layout ---
        # (This part remains largely the same)
        # Input Frame
        input_frame = tk.LabelFrame(master, text="Input Files", padx=10, pady=10)
        input_frame.pack(pady=10, padx=10, fill="x")
        self.listbox_files = tk.Listbox(input_frame, selectmode=tk.EXTENDED, width=60, height=8)
        self.listbox_files.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        scrollbar = tk.Scrollbar(input_frame, orient="vertical", command=self.listbox_files.yview)
        self.listbox_files.config(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.LEFT, fill="y")
        input_button_frame = tk.Frame(input_frame)
        input_button_frame.pack(side=tk.LEFT, fill="y")
        self.btn_add_files = tk.Button(input_button_frame, text="Add Files...", command=self.select_input_files)
        self.btn_add_files.pack(pady=2, fill="x")
        self.btn_clear_list = tk.Button(input_button_frame, text="Clear List", command=self.clear_input_list)
        self.btn_clear_list.pack(pady=2, fill="x")

        # Output Frame
        output_frame = tk.LabelFrame(master, text="Output Folder", padx=10, pady=10)
        output_frame.pack(pady=5, padx=10, fill="x")
        self.entry_output = tk.Entry(output_frame, textvariable=self.output_folder, width=50, state='readonly')
        self.entry_output.pack(side=tk.LEFT, fill="x", expand=True, padx=(0, 5))
        self.btn_browse_output = tk.Button(output_frame, text="Browse...", command=self.select_output_folder)
        self.btn_browse_output.pack(side=tk.LEFT)

        # Action Frame
        action_frame = tk.Frame(master, padx=10, pady=5)
        action_frame.pack(fill="x")
        self.btn_convert = tk.Button(action_frame, text="Convert to SVG", command=self.start_conversion, width=15, height=2)
        self.btn_convert.pack(pady=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(action_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill="x", pady=5)

        # Status Frame
        status_frame = tk.LabelFrame(master, text="Status Log", padx=10, pady=10)
        status_frame.pack(pady=5, padx=10, fill="both", expand=True)
        self.text_status = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, height=10, state='disabled')
        self.text_status.pack(fill="both", expand=True)

        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # --- !!! Start the queue polling loop !!! ---
        self.process_gui_queue()


    # --- !!! Method to Process Queue Items (Runs in Main Thread) !!! ---
    def process_gui_queue(self):
        """ Process items placed in the queue by the background thread. """
        try:
            while True: # Process all messages currently in the queue
                # Use get_nowait() to avoid blocking if the queue is empty
                task = self.gui_queue.get_nowait()

                try:
                    if task[0] == 'log':
                        self._log_message_gui(task[1])
                    elif task[0] == 'set_progress':
                        self.progress_var.set(task[1])
                    elif task[0] == 'show_error':
                        messagebox.showerror(task[1], task[2])
                    elif task[0] == 'show_info':
                        messagebox.showinfo(task[1], task[2])
                    elif task[0] == 'final_update':
                        # Call the original completion handler, now safely in main thread
                        self.on_complete_ui_update(*task[1:]) # Unpack args
                    # Add more task types if needed
                except Exception as e:
                    # Log errors happening during queue processing itself
                    print(f"Error processing GUI queue task {task}: {e}")
                    traceback.print_exc()

        except queue.Empty:
            # Queue is empty, do nothing this time
            pass
        finally:
            # --- !!! Reschedule the check !!! ---
            if self.master.winfo_exists(): # Only reschedule if window still exists
                self.master.after(100, self.process_gui_queue) # Check again in 100ms

    # --- Renamed Log function for actual GUI update (Main Thread ONLY) ---
    def _log_message_gui(self, message):
        """ Appends a message to the status text area. MUST be called from Main Thread. """
        if not self.master.winfo_exists(): return
        try:
            self.text_status.config(state='normal')
            self.text_status.insert(tk.END, str(message) + "\n") # Ensure message is string
            self.text_status.see(tk.END)
            self.text_status.config(state='disabled')
        except tk.TclError:
             pass # Widget might be destroyed

    # --- Log function called from anywhere (puts message in queue) ---
    def log_message(self, message):
        """ Safely logs a message by putting it into the GUI queue. """
        self.gui_queue.put(('log', message))

    # --- Other Methods (Mostly Unchanged, except start_conversion and convert_files_thread) ---
    def is_dvisvgm_available(self):
        return shutil.which("dvisvgm") is not None

    def select_input_files(self):
        # (No changes needed here)
        if self.conversion_running:
            messagebox.showwarning("Busy", "Cannot add files while conversion is running.")
            return
        # ... (rest of the function is fine) ...
        filetypes = [("Supported Files", "*.dvi *.eps *.pdf"),("DVI Files", "*.dvi"),("EPS Files", "*.eps"),("PDF Files", "*.pdf"),("All Files", "*.*")]
        selected = filedialog.askopenfilenames(title="Select Input Files",filetypes=filetypes)
        if selected:
            count_added = 0; count_skipped = 0
            unique_new_files = set(selected) - set(self.input_files)
            for file_path in selected:
                if file_path in unique_new_files:
                    if file_path.lower().endswith(self.allowed_extensions):
                        self.input_files.append(file_path); self.listbox_files.insert(tk.END, os.path.basename(file_path)); count_added += 1; unique_new_files.remove(file_path)
                    else: self.log_message(f"Skipped: '{os.path.basename(file_path)}' has an unsupported extension."); count_skipped += 1; unique_new_files.remove(file_path)
                elif file_path not in self.input_files: self.log_message(f"Skipped: '{os.path.basename(file_path)}' already in list."); count_skipped +=1
            if count_added > 0: self.log_message(f"Added {count_added} file(s) to the list.")
            if count_skipped > 0: self.log_message(f"Skipped {count_skipped} file(s) (duplicates or wrong type).")


    def clear_input_list(self):
         # (No changes needed here)
         if self.conversion_running: messagebox.showwarning("Busy", "Cannot clear list while conversion is running."); return
         self.input_files.clear(); self.listbox_files.delete(0, tk.END); self.log_message("Input file list cleared.")

    def select_output_folder(self):
        # (No changes needed here)
        if self.conversion_running: messagebox.showwarning("Busy", "Cannot change output folder while conversion is running."); return
        selected = filedialog.askdirectory(title="Select Output Folder")
        if selected: self.output_folder.set(selected); self.log_message(f"Output folder set to: {selected}")

    # --- start_conversion (Starts the thread, Main Thread) ---
    # (No major changes needed here from the previous fix)
    def start_conversion(self):
        """Validates inputs, gets Tkinter data, and starts the conversion thread."""
        if self.conversion_running:
            self.stop_conversion(); return

        if not self.input_files: messagebox.showerror("Error", "No input files selected."); return
        try: out_dir = self.output_folder.get()
        except tk.TclError as e: self.log_message(f"Error accessing output folder variable: {e}"); messagebox.showerror("Internal Error", "Could not read output folder setting."); return
        if not out_dir: messagebox.showerror("Error", "No output folder selected."); return
        if not os.path.isdir(out_dir): messagebox.showerror("Error", f"Output folder does not exist:\n{out_dir}"); return

        files_to_process = list(self.input_files)
        self.conversion_running = True
        self._set_ui_state(running=True)
        # Reset progress bar via queue
        self.gui_queue.put(('set_progress', 0))

        self.conversion_thread = threading.Thread(
            target=self.convert_files_thread,
            args=(out_dir, files_to_process),
            daemon=True)
        self.conversion_thread.start()


    # --- convert_files_thread (Background Thread - USES QUEUE) ---
    def convert_files_thread(self, out_dir, input_files_list):
        """ Performs dvisvgm conversion. Puts GUI updates into self.gui_queue. """
        # --- Log start via queue ---
        self.gui_queue.put(('log', "--- Starting Conversion ---"))

        success_count = 0; error_count = 0; skipped_count = 0
        total_files = len(input_files_list)
        self.dvisvgm_process = None

        for i, input_file in enumerate(input_files_list):
            if not self.conversion_running:
                skipped_count = total_files - i
                # --- Log stop via queue ---
                self.gui_queue.put(('log', f"Conversion stopped by user. Skipped {skipped_count} file(s)."))
                break

            # --- Update progress via queue ---
            progress_pct = ((i + 1) / total_files) * 100
            self.gui_queue.put(('set_progress', progress_pct))

            base_name = os.path.basename(input_file)
            name_without_ext = os.path.splitext(base_name)[0]
            output_svg_file = os.path.join(out_dir, f"{name_without_ext}.svg")

            # --- Log processing via queue ---
            log_msg = f"Processing ({i+1}/{total_files}): {base_name} -> {os.path.basename(output_svg_file)}"
            self.gui_queue.put(('log', log_msg))

            command = ["dvisvgm", "--output=" + output_svg_file, input_file]

            try:
                # --- Run Subprocess (Safe in background thread) ---
                process = subprocess.run(
                    command, capture_output=True, text=True, encoding='utf-8',
                    errors='replace', check=False,
                    creationflags=subprocess.CREATE_NO_WINDOW if platform.system() == "Windows" else 0)
                self.dvisvgm_process = None

                stderr_output = process.stderr.strip() if process.stderr else ""
                stdout_output = process.stdout.strip() if process.stdout else ""

                # --- Handle results and log via queue ---
                if process.returncode == 0:
                    success_count += 1
                    self.gui_queue.put(('log', f"  Success: Converted '{base_name}'"))
                    if "warning:" in stderr_output.lower():
                         self.gui_queue.put(('log', "    Warnings reported by dvisvgm (stderr):"))
                         for line in stderr_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                    if stdout_output:
                        self.gui_queue.put(('log',"    Info from dvisvgm (stdout):"))
                        for line in stdout_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                else:
                    error_count += 1
                    interpreted_error = self.parse_dvisvgm_error(stderr_output)
                    self.gui_queue.put(('log', f"  ERROR converting '{base_name}' (Exit Code: {process.returncode})"))
                    self.gui_queue.put(('log', f"    Interpretation: {interpreted_error}"))
                    if stderr_output:
                        self.gui_queue.put(('log', "    --- Raw dvisvgm stderr: ---"))
                        for line in stderr_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                        self.gui_queue.put(('log', "    --- End Raw stderr ---"))
                    else: self.gui_queue.put(('log', "    (No output captured on stderr)"))
                    if stdout_output:
                         self.gui_queue.put(('log',"    --- Raw dvisvgm stdout: ---"))
                         for line in stdout_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                         self.gui_queue.put(('log', "    --- End Raw stdout ---"))

            except FileNotFoundError:
                error_count += (total_files - success_count - skipped_count)
                self.gui_queue.put(('log', "  FATAL ERROR: 'dvisvgm' command not found. Aborting conversion."))
                # --- Show error message box via queue ---
                self.gui_queue.put(('show_error', "Error", "'dvisvgm' command not found.\nPlease ensure it's installed and in your system's PATH."))
                break

            except OSError as e:
                 error_count += 1
                 self.gui_queue.put(('log', f"  OS ERROR running dvisvgm for '{base_name}': {e}"))
                 self.gui_queue.put(('log', "    Check permissions and if dvisvgm can be executed."))
                 self.dvisvgm_process = None

            except Exception as e:
                error_count += 1
                tb_str = traceback.format_exc()
                self.gui_queue.put(('log', f"  PYTHON ERROR during processing of '{base_name}': {e}"))
                self.gui_queue.put(('log', tb_str)) # Log traceback
                self.dvisvgm_process = None
            finally:
                 self.dvisvgm_process = None

        # --- Conversion Finished - Put final update task in queue ---
        # Pass final counts as arguments to the 'final_update' task
        self.gui_queue.put(('final_update', success_count, error_count, skipped_count, total_files))


    # --- on_complete_ui_update (Main Thread - Called by queue processor) ---
    # (No changes needed here from previous fix)
    def on_complete_ui_update(self, success_count, error_count, skipped_count, total_files):
        """ Updates the UI after the conversion thread finishes. Called via queue. """
        if not self.master.winfo_exists(): return

        # Use _log_message_gui for direct logging here as we are in the main thread
        self._log_message_gui("--- Conversion Finished ---")
        self._log_message_gui(f"Successfully converted: {success_count} file(s)")
        self._log_message_gui(f"Errors encountered: {error_count} file(s)")
        if skipped_count > 0:
             self._log_message_gui(f"Skipped (due to stop): {skipped_count} file(s)")

        final_progress = 100 if (success_count + error_count + skipped_count) == total_files else self.progress_var.get()
        self.progress_var.set(final_progress) # Set progress directly

        if self.conversion_running: # Finished naturally
            self.conversion_running = False
            self._set_ui_state(running=False)
        # If stopped externally, _set_ui_state was already called by stop_conversion

        processed_count = success_count + error_count
        total_attempted = processed_count + skipped_count
        summary_msg = f"Finished processing {processed_count} of {total_attempted} file(s)."
        if skipped_count > 0: summary_msg += f" ({skipped_count} skipped due to stop)."

        # Show final message box directly
        messagebox.showinfo("Conversion Complete", f"{summary_msg}\n\nSuccess: {success_count}\nErrors: {error_count}\n\nCheck the status log for details.")


    # --- stop_conversion (Main Thread) ---
    # (Minor change to use direct logging)
    def stop_conversion(self):
        """Signals the conversion thread to stop."""
        if self.conversion_running and self.conversion_thread and self.conversion_thread.is_alive():
            # Log directly as this runs in main thread
            self._log_message_gui("--- Stopping conversion ---")
            self.conversion_running = False
            self._set_ui_state(running=False) # Update UI immediately
            # Temporarily show stopping text - this is safe
            self.btn_convert.config(text="Stopping...")
            self.master.after(500, self._check_thread_stopped)
        # ... (rest is same) ...
        else:
            if self.conversion_running: self._log_message_gui("Stop requested, but thread seems inactive. Resetting UI."); self.conversion_running = False; self._set_ui_state(running=False)
            else: self._log_message_gui("No active conversion to stop.")

    # --- _check_thread_stopped (Main Thread) ---
    # (Minor change to use direct logging)
    def _check_thread_stopped(self):
        """Checks if the thread has finished after a stop request."""
        if not self.conversion_running:
             if not self.conversion_thread or not self.conversion_thread.is_alive():
                  self._log_message_gui("Conversion stopped.") # Log directly
                  self._set_ui_state(running=False)
             else:
                  self._log_message_gui("Waiting for conversion thread to terminate...") # Log directly
                  self.master.after(500, self._check_thread_stopped) # Check again

    # --- _set_ui_state (Main Thread) ---
    # (No changes needed)
    def _set_ui_state(self, running: bool):
        state = tk.DISABLED if running else tk.NORMAL
        button_text = "Stop Conversion" if running else "Convert to SVG"
        try:
            if self.btn_convert.winfo_exists(): self.btn_convert.config(text=button_text, state=tk.NORMAL)
            if self.btn_add_files.winfo_exists(): self.btn_add_files.config(state=state)
            if self.btn_clear_list.winfo_exists(): self.btn_clear_list.config(state=state)
            if self.btn_browse_output.winfo_exists(): self.btn_browse_output.config(state=state)
        except tk.TclError: pass

    # --- parse_dvisvgm_error (Utility - OK to call from background thread) ---
    # (No changes needed)
    def parse_dvisvgm_error(self, stderr_text):
        if not stderr_text: return "No specific error message captured from dvisvgm."
        stderr_lower = stderr_text.lower()
        if "font" in stderr_lower and "not found" in stderr_lower: match = re.search(r"font\s+['\"]?([^'\"\s]+)['\"]?\s+not found", stderr_lower); font_name = match.group(1) if match else "unknown"; return f"Missing Font: '{font_name}'. Ensure the required font is installed and accessible by dvisvgm/kpathsea."
        if "error" in stderr_lower and "loading font" in stderr_lower: return "Error loading a font. The font file might be corrupted or incompatible."
        if "can't open file" in stderr_lower or "cannot open input file" in stderr_lower: match = re.search(r"(?:can't open file|cannot open input file)\s*['\"]?([^'\"\s]+)['\"]?", stderr_lower); fname = f" '{match.group(1)}'" if match else ""; return f"Cannot open input file{fname}. Check if the file exists, is not corrupted, and has read permissions."
        if "invalid argument" in stderr_lower: return "Invalid argument passed to dvisvgm (internal program error or bad command)."
        if "i/o error" in stderr_lower: return "Input/Output error. Check file permissions, disk space, or if the file is locked."
        if "permission denied" in stderr_lower: return "Permission denied. Check read permissions for the input file and write permissions for the output folder."
        if "postscript error" in stderr_lower or "ghostscript" in stderr_lower or "ps interpreter failed" in stderr_lower: return "Error processing PostScript/EPS data. Ghostscript might be needed/misconfigured, or the PS/EPS file has issues."
        if "pdf parser error" in stderr_lower: return "Error parsing PDF file. The PDF might be corrupted, password-protected, or use unsupported features."
        if "error" in stderr_lower and "processing dvi specials" in stderr_lower: return "Error processing DVI specials. The DVI might reference external files incorrectly or use unsupported specials."
        match = re.search(r"^(?:error|fatal error):\s*(.*)", stderr_text, re.MULTILINE | re.IGNORECASE)
        if match: return f"General Error: {match.group(1).strip()}"
        return "Unknown error occurred during conversion. Check the raw output below."


    # --- Signal Handling and Closing (Mostly unchanged, direct logging ok in signal/close handlers if careful) ---
    def signal_handler(self, sig, frame):
        print("\nCtrl+C detected, shutting down gracefully...")
        if self.master.winfo_exists():
             # Use _log_message_gui here if you want it in the window, but print might be safer during shutdown
             # self._log_message_gui("Ctrl+C detected, shutting down...") # Maybe omit this
             self.master.after(0, self.on_closing) # Schedule on_closing
        else: sys.exit(1)

    def on_closing(self):
        if self.conversion_running:
            if messagebox.askyesno("Confirm Exit", "Conversion is in progress. Are you sure you want to exit? The current file might not complete."):
                self.conversion_running = False
                self._log_message_gui("--- Exiting application - Attempting to stop conversion ---") # Direct log ok here
                self.master.after(200, self._check_before_destroy)
            else: return
        else: self.master.destroy()

    def _check_before_destroy(self):
        if self.conversion_thread and self.conversion_thread.is_alive():
            self._log_message_gui("Conversion thread still active. Forcing exit.") # Direct log ok here
        self.master.destroy()


# --- Main Execution ---
# (No changes needed here)
if __name__ == "__main__":
    root = tk.Tk()
    app = DviSvgConverterApp(root)

    if root.winfo_exists():
        try:
            while root.winfo_exists():
                root.update()
                root.update_idletasks()
        except tk.TclError as e:
             if "application has been destroyed" not in str(e): print(f"Tkinter TclError: {e}"); traceback.print_exc()
        except KeyboardInterrupt:
             print("\nCtrl+C detected in main loop, initiating shutdown...")
             if app and app.master.winfo_exists(): app.on_closing()
             elif root.winfo_exists(): root.destroy()
        except Exception as e:
            print(f"An unexpected error occurred in the main loop: {e}"); traceback.print_exc()
            if root.winfo_exists(): root.destroy()
            sys.exit(1)