#!/usr/bin/env python3
"""Simple test to verify the fixes work."""

import sys
import os

def test_application_starts():
    """Test that the application starts without errors."""
    print("Testing application startup...")
    
    # Try to import and create the application
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("dvisvgm_gui", "DVISVGM GUI.py")
        dvisvgm_module = importlib.util.module_from_spec(spec)
        
        # Check if the module loads without syntax errors
        spec.loader.exec_module(dvisvgm_module)
        print("✓ Module loads successfully")
        
        # Check if the class exists
        if hasattr(dvisvgm_module, 'DviSvgConverterApp'):
            print("✓ DviSvgConverterApp class found")
        else:
            print("✗ DviSvgConverterApp class not found")
            return False
            
        # Check if constants are defined
        expected_constants = ['MAX_QUEUE_ITEMS_PER_CYCLE', 'QUEUE_POLLING_INTERVAL_MS', 'MAX_PATH_LENGTH']
        for const in expected_constants:
            if hasattr(dvisvgm_module, const):
                print(f"✓ Constant {const} defined")
            else:
                print(f"✗ Constant {const} not found")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Application startup test failed: {e}")
        return False

def test_file_existence():
    """Test that test files exist for validation."""
    print("\nTesting test files...")
    
    test_files = ['test.dvi', 'test.txt']
    for file in test_files:
        if os.path.exists(file):
            print(f"✓ Test file {file} exists")
        else:
            print(f"✗ Test file {file} missing")
            return False
    
    return True

def main():
    """Run simple tests."""
    print("=" * 50)
    print("SIMPLE VERIFICATION OF HIGH PRIORITY FIXES")
    print("=" * 50)
    
    tests = [
        test_application_starts,
        test_file_existence,
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("✓ Basic verification passed - fixes appear to be working")
        print("✓ Application can be imported and constants are defined")
        print("✓ Ready to proceed with medium priority improvements")
    else:
        print("✗ Some basic tests failed")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
