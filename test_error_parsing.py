#!/usr/bin/env python3
"""Test the refactored error parsing functionality."""

import sys
import importlib.util

def test_error_parsing():
    """Test the refactored error parsing methods."""
    print("Testing refactored error parsing...")
    
    # Import the module
    spec = importlib.util.spec_from_file_location("dvisvgm_gui", "DVISVGM GUI.py")
    dvisvgm_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(dvisvgm_module)
    
    # Create a mock app instance (without GUI)
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = dvisvgm_module.DviSvgConverterApp(root)
        
        # Test cases
        test_cases = [
            ("font 'Arial' not found", "Missing Font"),
            ("can't open file test.dvi", "Cannot open input file"),
            ("permission denied", "Permission denied"),
            ("pdf parser error", "Error parsing PDF"),
            ("postscript error", "Error processing PostScript"),
            ("invalid argument", "Invalid argument"),
            ("error: something went wrong", "General Error"),
            ("", "No specific error message"),
            ("unknown weird error", "Unknown error"),
        ]
        
        print("\nTesting error parsing patterns:")
        all_passed = True
        
        for stderr_input, expected_keyword in test_cases:
            try:
                result = app.parse_dvisvgm_error(stderr_input)
                if expected_keyword.lower() in result.lower():
                    print(f"✓ '{stderr_input}' -> correctly parsed (contains '{expected_keyword}')")
                else:
                    print(f"✗ '{stderr_input}' -> unexpected result: {result}")
                    all_passed = False
            except Exception as e:
                print(f"✗ '{stderr_input}' -> parsing failed with error: {e}")
                all_passed = False
        
        return all_passed
        
    finally:
        root.destroy()

def main():
    """Run error parsing tests."""
    print("=" * 60)
    print("TESTING REFACTORED ERROR PARSING")
    print("=" * 60)
    
    success = test_error_parsing()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ Error parsing refactoring successful!")
        print("✓ All error patterns are correctly handled")
        print("✓ Code is now more maintainable and organized")
    else:
        print("✗ Some error parsing tests failed")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
