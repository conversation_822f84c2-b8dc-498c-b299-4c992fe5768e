# Required imports remain the same, add queue
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
import subprocess
import os
import platform
import shutil
import threading
import signal
import sys
import re
import traceback
import queue # Import the queue module
import logging
import logging.handlers
from datetime import datetime

# Configuration Constants
MAX_QUEUE_ITEMS_PER_CYCLE = 10  # Maximum queue items to process per GUI update cycle
QUEUE_POLLING_INTERVAL_MS = 100  # Queue polling interval in milliseconds
MAX_PATH_LENGTH = 260  # Maximum file path length (Windows limit)
GUI_WIDGET_WIDTH = 60  # Default width for GUI widgets
STATUS_LOG_HEIGHT = 10  # Height of status log text area

# Logging Configuration
LOG_FILE_NAME = "dvisvgm_gui.log"  # Log file name
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB max log file size
LOG_BACKUP_COUNT = 5  # Number of backup log files to keep
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # Log message format

# GUI Layout Constants
WINDOW_WIDTH = 600  # Main window width
WINDOW_HEIGHT = 470  # Main window height
FRAME_PADDING = 10  # Standard padding for frames
BUTTON_PADDING = 2  # Padding for buttons
ENTRY_WIDTH = 50  # Width for entry widgets
CONVERT_BUTTON_WIDTH = 15  # Convert button width
CONVERT_BUTTON_HEIGHT = 2  # Convert button height
LISTBOX_HEIGHT = 8  # Height of file listbox
PROGRESS_BAR_MAX = 100  # Maximum value for progress bar

# File Processing Constants
MIN_DISK_SPACE_MB = 100  # Minimum required disk space in MB
SUBPROCESS_TIMEOUT_SECONDS = 300  # 5 minutes timeout for subprocess
PROCESS_TERMINATION_TIMEOUT = 5  # Seconds to wait for graceful termination
PROCESS_KILL_TIMEOUT = 10  # Seconds to wait before force kill
MAX_VALIDATION_ERRORS_DISPLAY = 5  # Maximum validation errors to show in popup

# UI Update Constants
THREAD_CHECK_INTERVAL_MS = 500  # Interval for checking thread status
APP_CLOSE_DELAY_MS = 200  # Delay before destroying app on close

class DviSvgConverterApp:
    def __init__(self, master):
        self.master = master
        master.title("DVI/EPS/PDF to SVG Converter")
        master.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")

        # Setup logging first
        self.setup_logging()
        self.logger = logging.getLogger("DviSvgConverter")
        self.logger.info("Application starting up")

        signal.signal(signal.SIGINT, self.signal_handler)

        self.input_files = []
        self.output_folder = tk.StringVar()
        self.allowed_extensions = ('.dvi', '.eps', '.pdf')
        self.conversion_running = False
        self.conversion_thread = None
        self.dvisvgm_process = None

        # --- !!! Create the Queue for thread communication !!! ---
        self.gui_queue = queue.Queue()

        # --- Check for dvisvgm ---
        # (This part remains the same)
        if not self.is_dvisvgm_available():
            messagebox.showerror("Error",
                                 "'dvisvgm' command not found.\nPlease install it (usually part of TeX Live/MiKTeX) and ensure it's in your system's PATH.")
            master.destroy()
            return

        # --- GUI Layout ---
        # (This part remains largely the same)
        # Input Frame
        input_frame = tk.LabelFrame(master, text="Input Files", padx=FRAME_PADDING, pady=FRAME_PADDING)
        input_frame.pack(pady=FRAME_PADDING, padx=FRAME_PADDING, fill="x")
        self.listbox_files = tk.Listbox(input_frame, selectmode=tk.EXTENDED, width=GUI_WIDGET_WIDTH, height=LISTBOX_HEIGHT)
        self.listbox_files.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        scrollbar = tk.Scrollbar(input_frame, orient="vertical", command=self.listbox_files.yview)
        self.listbox_files.config(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.LEFT, fill="y")
        input_button_frame = tk.Frame(input_frame)
        input_button_frame.pack(side=tk.LEFT, fill="y")
        self.btn_add_files = tk.Button(input_button_frame, text="Add Files...", command=self.select_input_files)
        self.btn_add_files.pack(pady=BUTTON_PADDING, fill="x")
        self.btn_clear_list = tk.Button(input_button_frame, text="Clear List", command=self.clear_input_list)
        self.btn_clear_list.pack(pady=BUTTON_PADDING, fill="x")

        # Output Frame
        output_frame = tk.LabelFrame(master, text="Output Folder", padx=FRAME_PADDING, pady=FRAME_PADDING)
        output_frame.pack(pady=5, padx=FRAME_PADDING, fill="x")
        self.entry_output = tk.Entry(output_frame, textvariable=self.output_folder, width=ENTRY_WIDTH, state='readonly')
        self.entry_output.pack(side=tk.LEFT, fill="x", expand=True, padx=(0, 5))
        self.btn_browse_output = tk.Button(output_frame, text="Browse...", command=self.select_output_folder)
        self.btn_browse_output.pack(side=tk.LEFT)

        # Action Frame
        action_frame = tk.Frame(master, padx=FRAME_PADDING, pady=5)
        action_frame.pack(fill="x")
        self.btn_convert = tk.Button(action_frame, text="Convert to SVG", command=self.start_conversion, width=CONVERT_BUTTON_WIDTH, height=CONVERT_BUTTON_HEIGHT)
        self.btn_convert.pack(pady=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(action_frame, variable=self.progress_var, maximum=PROGRESS_BAR_MAX)
        self.progress_bar.pack(fill="x", pady=5)

        # Status Frame
        status_frame = tk.LabelFrame(master, text="Status Log", padx=FRAME_PADDING, pady=FRAME_PADDING)
        status_frame.pack(pady=5, padx=10, fill="both", expand=True)
        self.text_status = scrolledtext.ScrolledText(status_frame, wrap=tk.WORD, height=STATUS_LOG_HEIGHT, state='disabled')
        self.text_status.pack(fill="both", expand=True)

        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # --- !!! Start the queue polling loop !!! ---
        self.process_gui_queue()

    # --- Logging Setup ---
    def setup_logging(self):
        """
        Setup comprehensive logging system with file rotation and multiple levels.
        """
        try:
            # Create logs directory if it doesn't exist
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            log_file_path = os.path.join(log_dir, LOG_FILE_NAME)

            # Configure root logger
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.DEBUG)

            # Remove existing handlers to avoid duplicates
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)

            # Create rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                log_file_path,
                maxBytes=LOG_MAX_SIZE,
                backupCount=LOG_BACKUP_COUNT,
                encoding='utf-8'
            )
            file_handler.setLevel(logging.DEBUG)

            # Create console handler for errors and warnings
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.WARNING)

            # Create formatter
            formatter = logging.Formatter(LOG_FORMAT)
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            # Add handlers to root logger
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)

            # Log setup completion
            logging.info("Logging system initialized successfully")
            logging.info(f"Log file: {log_file_path}")

        except Exception as e:
            # If logging setup fails, print to console and continue
            print(f"Warning: Failed to setup logging: {e}")
            print("Application will continue without file logging")

    # --- Input Validation Methods ---
    def validate_file_path(self, file_path):
        """
        Comprehensive validation of file paths.
        Returns (is_valid, error_message)
        """
        if not file_path:
            return False, "File path is empty"

        # Check path length
        if len(file_path) > MAX_PATH_LENGTH:
            return False, f"File path exceeds maximum length of {MAX_PATH_LENGTH} characters"

        # Check if file exists
        if not os.path.isfile(file_path):
            return False, f"File does not exist: {os.path.basename(file_path)}"

        # Check file extension
        if not file_path.lower().endswith(self.allowed_extensions):
            return False, f"Unsupported file extension: {os.path.splitext(file_path)[1]}"

        # Check file permissions
        if not os.access(file_path, os.R_OK):
            return False, f"No read permission for file: {os.path.basename(file_path)}"

        # Check for potentially dangerous characters in path
        dangerous_chars = ['<', '>', '|', '"', '*', '?']
        if any(char in file_path for char in dangerous_chars):
            return False, f"File path contains potentially dangerous characters: {os.path.basename(file_path)}"

        return True, ""

    def validate_output_directory(self, dir_path):
        """
        Comprehensive validation of output directory.
        Returns (is_valid, error_message)
        """
        if not dir_path:
            return False, "Output directory is not specified"

        # Check path length
        if len(dir_path) > MAX_PATH_LENGTH:
            return False, f"Directory path exceeds maximum length of {MAX_PATH_LENGTH} characters"

        # Check if directory exists
        if not os.path.isdir(dir_path):
            return False, f"Output directory does not exist: {dir_path}"

        # Check write permissions
        if not os.access(dir_path, os.W_OK):
            return False, f"No write permission for output directory: {dir_path}"

        # Check available disk space (at least 100MB)
        try:
            if platform.system() == "Windows":
                import shutil
                _, _, free = shutil.disk_usage(dir_path)
                if free < MIN_DISK_SPACE_MB * 1024 * 1024:  # Convert MB to bytes
                    return False, f"Insufficient disk space in output directory (less than {MIN_DISK_SPACE_MB}MB available)"
        except Exception:
            # If we can't check disk space, continue anyway
            pass

        # Check for potentially dangerous characters in path
        dangerous_chars = ['<', '>', '|', '"', '*', '?']
        if any(char in dir_path for char in dangerous_chars):
            return False, f"Directory path contains potentially dangerous characters"

        return True, ""

    def validate_conversion_inputs(self):
        """
        Validates all inputs before starting conversion.
        Returns (is_valid, error_messages_list)
        """
        errors = []

        # Validate input files
        if not self.input_files:
            errors.append("No input files selected")
        else:
            for file_path in self.input_files:
                is_valid, error_msg = self.validate_file_path(file_path)
                if not is_valid:
                    errors.append(error_msg)

        # Validate output directory
        try:
            out_dir = self.output_folder.get()
            is_valid, error_msg = self.validate_output_directory(out_dir)
            if not is_valid:
                errors.append(error_msg)
        except tk.TclError as e:
            errors.append(f"Error accessing output folder setting: {e}")

        return len(errors) == 0, errors

    # --- !!! Method to Process Queue Items (Runs in Main Thread) !!! ---
    def process_gui_queue(self):
        """
        Process items placed in the queue by the background thread.
        Limits processing to MAX_QUEUE_ITEMS_PER_CYCLE items per call to prevent GUI freezing.
        """
        items_processed = 0

        try:
            # Process up to MAX_QUEUE_ITEMS_PER_CYCLE items per call
            while items_processed < MAX_QUEUE_ITEMS_PER_CYCLE:
                try:
                    # Use get_nowait() to avoid blocking if the queue is empty
                    task = self.gui_queue.get_nowait()
                    items_processed += 1

                    # Process the task
                    try:
                        if task[0] == 'log':
                            self._log_message_gui(task[1])
                        elif task[0] == 'set_progress':
                            self.progress_var.set(task[1])
                        elif task[0] == 'show_error':
                            messagebox.showerror(task[1], task[2])
                        elif task[0] == 'show_info':
                            messagebox.showinfo(task[1], task[2])
                        elif task[0] == 'final_update':
                            # Call the original completion handler, now safely in main thread
                            self.on_complete_ui_update(*task[1:]) # Unpack args
                        else:
                            print(f"Warning: Unknown queue task type: {task[0]}")
                    except Exception as e:
                        # Log errors happening during queue processing itself
                        print(f"Error processing GUI queue task {task}: {e}")
                        traceback.print_exc()

                except queue.Empty:
                    # Queue is empty, exit the processing loop
                    break

        except Exception as e:
            # Handle any unexpected errors in the queue processing loop
            print(f"Unexpected error in process_gui_queue: {e}")
            traceback.print_exc()
        finally:
            # --- !!! Reschedule the check !!! ---
            if self.master.winfo_exists(): # Only reschedule if window still exists
                self.master.after(QUEUE_POLLING_INTERVAL_MS, self.process_gui_queue)

    # --- Renamed Log function for actual GUI update (Main Thread ONLY) ---
    def _log_message_gui(self, message):
        """ Appends a message to the status text area. MUST be called from Main Thread. """
        if not self.master.winfo_exists(): return
        try:
            self.text_status.config(state='normal')
            self.text_status.insert(tk.END, str(message) + "\n") # Ensure message is string
            self.text_status.see(tk.END)
            self.text_status.config(state='disabled')
        except tk.TclError:
             pass # Widget might be destroyed

    # --- Enhanced Logging Functions ---
    def log_message(self, message, level="INFO"):
        """
        Safely logs a message to both GUI and file.
        Args:
            message: The message to log
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        # Log to file with appropriate level
        if hasattr(self, 'logger'):
            log_method = getattr(self.logger, level.lower(), self.logger.info)
            log_method(message)

        # Log to GUI (always show in GUI regardless of level)
        self.gui_queue.put(('log', message))

    def log_debug(self, message):
        """Log debug message."""
        self.log_message(message, "DEBUG")

    def log_info(self, message):
        """Log info message."""
        self.log_message(message, "INFO")

    def log_warning(self, message):
        """Log warning message."""
        self.log_message(message, "WARNING")

    def log_error(self, message):
        """Log error message."""
        self.log_message(message, "ERROR")

    def log_critical(self, message):
        """Log critical message."""
        self.log_message(message, "CRITICAL")

    # --- Other Methods (Mostly Unchanged, except start_conversion and convert_files_thread) ---
    def is_dvisvgm_available(self):
        return shutil.which("dvisvgm") is not None

    def select_input_files(self):
        """Select input files with comprehensive validation."""
        if self.conversion_running:
            messagebox.showwarning("Busy", "Cannot add files while conversion is running.")
            return

        filetypes = [
            ("Supported Files", "*.dvi *.eps *.pdf"),
            ("DVI Files", "*.dvi"),
            ("EPS Files", "*.eps"),
            ("PDF Files", "*.pdf"),
            ("All Files", "*.*")
        ]
        selected = filedialog.askopenfilenames(title="Select Input Files", filetypes=filetypes)

        if selected:
            count_added = 0
            count_skipped = 0
            validation_errors = []

            for file_path in selected:
                # Skip if already in list
                if file_path in self.input_files:
                    self.log_message(f"Skipped: '{os.path.basename(file_path)}' already in list.")
                    count_skipped += 1
                    continue

                # Validate the file
                is_valid, error_msg = self.validate_file_path(file_path)
                if is_valid:
                    self.input_files.append(file_path)
                    self.listbox_files.insert(tk.END, os.path.basename(file_path))
                    count_added += 1
                else:
                    self.log_warning(f"Skipped: {error_msg}")
                    validation_errors.append(error_msg)
                    count_skipped += 1

            # Report results
            if count_added > 0:
                self.log_info(f"Added {count_added} file(s) to the list.")
            if count_skipped > 0:
                self.log_warning(f"Skipped {count_skipped} file(s) (duplicates, validation errors, or wrong type).")

            # Show validation errors if any
            if validation_errors and len(validation_errors) <= 5:  # Don't overwhelm with too many errors
                error_text = "Some files were skipped due to validation errors:\n\n" + "\n".join(f"• {err}" for err in validation_errors)
                messagebox.showwarning("File Validation Issues", error_text)


    def clear_input_list(self):
         # (No changes needed here)
         if self.conversion_running: messagebox.showwarning("Busy", "Cannot clear list while conversion is running."); return
         self.input_files.clear(); self.listbox_files.delete(0, tk.END); self.log_message("Input file list cleared.")

    def select_output_folder(self):
        # (No changes needed here)
        if self.conversion_running: messagebox.showwarning("Busy", "Cannot change output folder while conversion is running."); return
        selected = filedialog.askdirectory(title="Select Output Folder")
        if selected: self.output_folder.set(selected); self.log_message(f"Output folder set to: {selected}")

    # --- start_conversion (Starts the thread, Main Thread) ---
    def start_conversion(self):
        """Validates inputs comprehensively and starts the conversion thread."""
        if self.conversion_running:
            self.stop_conversion()
            return

        # Comprehensive input validation
        is_valid, error_messages = self.validate_conversion_inputs()
        if not is_valid:
            error_text = "Validation failed:\n\n" + "\n".join(f"• {msg}" for msg in error_messages)
            messagebox.showerror("Input Validation Error", error_text)
            self.log_error("Conversion aborted due to validation errors:")
            for msg in error_messages:
                self.log_error(f"  - {msg}")
            return

        # Get validated output directory
        out_dir = self.output_folder.get()
        files_to_process = list(self.input_files)

        self.log_info(f"Starting conversion of {len(files_to_process)} file(s) to: {out_dir}")
        self.conversion_running = True
        self._set_ui_state(running=True)
        # Reset progress bar via queue
        self.gui_queue.put(('set_progress', 0))

        self.conversion_thread = threading.Thread(
            target=self.convert_files_thread,
            args=(out_dir, files_to_process),
            daemon=True)
        self.conversion_thread.start()


    # --- convert_files_thread (Background Thread - USES QUEUE) ---
    def convert_files_thread(self, out_dir, input_files_list):
        """ Performs dvisvgm conversion. Puts GUI updates into self.gui_queue. """
        # --- Log start via queue ---
        self.gui_queue.put(('log', "--- Starting Conversion ---"))

        success_count = 0; error_count = 0; skipped_count = 0
        total_files = len(input_files_list)
        self.dvisvgm_process = None

        for i, input_file in enumerate(input_files_list):
            if not self.conversion_running:
                skipped_count = total_files - i
                # --- Log stop via queue ---
                self.gui_queue.put(('log', f"Conversion stopped by user. Skipped {skipped_count} file(s)."))
                break

            # --- Update progress via queue ---
            progress_pct = ((i + 1) / total_files) * 100
            self.gui_queue.put(('set_progress', progress_pct))

            base_name = os.path.basename(input_file)
            name_without_ext = os.path.splitext(base_name)[0]
            output_svg_file = os.path.join(out_dir, f"{name_without_ext}.svg")

            # --- Log processing via queue ---
            log_msg = f"Processing ({i+1}/{total_files}): {base_name} -> {os.path.basename(output_svg_file)}"
            self.gui_queue.put(('log', log_msg))

            command = ["dvisvgm", "--output=" + output_svg_file, input_file]

            try:
                # --- Run Subprocess with proper process management ---
                # Use Popen for better process control and ability to terminate if needed
                self.dvisvgm_process = subprocess.Popen(
                    command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    creationflags=subprocess.CREATE_NO_WINDOW if platform.system() == "Windows" else 0
                )

                # Wait for process completion and get output
                try:
                    stdout_output, stderr_output = self.dvisvgm_process.communicate(timeout=300)  # 5 minute timeout
                    return_code = self.dvisvgm_process.returncode
                except subprocess.TimeoutExpired:
                    # Handle timeout - terminate the process
                    self.gui_queue.put(('log', f"  TIMEOUT: Process for '{base_name}' exceeded 5 minutes, terminating..."))
                    self.dvisvgm_process.terminate()
                    try:
                        self.dvisvgm_process.wait(timeout=10)  # Wait up to 10 seconds for graceful termination
                    except subprocess.TimeoutExpired:
                        self.dvisvgm_process.kill()  # Force kill if it doesn't terminate gracefully
                    error_count += 1
                    self.gui_queue.put(('log', f"  ERROR: Conversion of '{base_name}' timed out and was terminated"))
                    continue
                finally:
                    self.dvisvgm_process = None

                stderr_output = stderr_output.strip() if stderr_output else ""
                stdout_output = stdout_output.strip() if stdout_output else ""

                # --- Handle results and log via queue ---
                if return_code == 0:
                    success_count += 1
                    self.gui_queue.put(('log', f"  Success: Converted '{base_name}'"))
                    if "warning:" in stderr_output.lower():
                         self.gui_queue.put(('log', "    Warnings reported by dvisvgm (stderr):"))
                         for line in stderr_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                    if stdout_output:
                        self.gui_queue.put(('log',"    Info from dvisvgm (stdout):"))
                        for line in stdout_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                else:
                    error_count += 1
                    interpreted_error = self.parse_dvisvgm_error(stderr_output)
                    self.gui_queue.put(('log', f"  ERROR converting '{base_name}' (Exit Code: {return_code})"))
                    self.gui_queue.put(('log', f"    Interpretation: {interpreted_error}"))
                    if stderr_output:
                        self.gui_queue.put(('log', "    --- Raw dvisvgm stderr: ---"))
                        for line in stderr_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                        self.gui_queue.put(('log', "    --- End Raw stderr ---"))
                    else: self.gui_queue.put(('log', "    (No output captured on stderr)"))
                    if stdout_output:
                         self.gui_queue.put(('log',"    --- Raw dvisvgm stdout: ---"))
                         for line in stdout_output.splitlines(): self.gui_queue.put(('log', f"      {line}"))
                         self.gui_queue.put(('log', "    --- End Raw stdout ---"))

            except FileNotFoundError:
                # Count this as one error for the current file
                error_count += 1
                # Calculate how many files will be skipped (remaining files after current one)
                remaining_files = total_files - (i + 1)
                skipped_count += remaining_files

                self.gui_queue.put(('log', f"  FATAL ERROR: 'dvisvgm' command not found for '{base_name}'. Aborting conversion."))
                self.gui_queue.put(('log', f"  Remaining {remaining_files} file(s) will be skipped."))
                # --- Show error message box via queue ---
                self.gui_queue.put(('show_error', "Error", "'dvisvgm' command not found.\nPlease ensure it's installed and in your system's PATH."))
                break

            except OSError as e:
                 error_count += 1
                 self.gui_queue.put(('log', f"  OS ERROR running dvisvgm for '{base_name}': {e}"))
                 self.gui_queue.put(('log', "    Check permissions and if dvisvgm can be executed."))
                 self.dvisvgm_process = None

            except Exception as e:
                error_count += 1
                tb_str = traceback.format_exc()
                self.gui_queue.put(('log', f"  PYTHON ERROR during processing of '{base_name}': {e}"))
                self.gui_queue.put(('log', tb_str)) # Log traceback
                self.dvisvgm_process = None
            finally:
                 self.dvisvgm_process = None

        # --- Conversion Finished - Put final update task in queue ---
        # Pass final counts as arguments to the 'final_update' task
        self.gui_queue.put(('final_update', success_count, error_count, skipped_count, total_files))


    # --- on_complete_ui_update (Main Thread - Called by queue processor) ---
    # (No changes needed here from previous fix)
    def on_complete_ui_update(self, success_count, error_count, skipped_count, total_files):
        """ Updates the UI after the conversion thread finishes. Called via queue. """
        if not self.master.winfo_exists(): return

        # Use _log_message_gui for direct logging here as we are in the main thread
        self._log_message_gui("--- Conversion Finished ---")
        self._log_message_gui(f"Successfully converted: {success_count} file(s)")
        self._log_message_gui(f"Errors encountered: {error_count} file(s)")
        if skipped_count > 0:
             self._log_message_gui(f"Skipped (due to stop): {skipped_count} file(s)")

        final_progress = 100 if (success_count + error_count + skipped_count) == total_files else self.progress_var.get()
        self.progress_var.set(final_progress) # Set progress directly

        if self.conversion_running: # Finished naturally
            self.conversion_running = False
            self._set_ui_state(running=False)
        # If stopped externally, _set_ui_state was already called by stop_conversion

        processed_count = success_count + error_count
        total_attempted = processed_count + skipped_count
        summary_msg = f"Finished processing {processed_count} of {total_attempted} file(s)."
        if skipped_count > 0: summary_msg += f" ({skipped_count} skipped due to stop)."

        # Show final message box directly
        messagebox.showinfo("Conversion Complete", f"{summary_msg}\n\nSuccess: {success_count}\nErrors: {error_count}\n\nCheck the status log for details.")


    # --- stop_conversion (Main Thread) ---
    def stop_conversion(self):
        """Signals the conversion thread to stop and terminates any running subprocess."""
        if self.conversion_running and self.conversion_thread and self.conversion_thread.is_alive():
            # Log directly as this runs in main thread
            self._log_message_gui("--- Stopping conversion ---")

            # Terminate any running dvisvgm process
            if self.dvisvgm_process:
                try:
                    self._log_message_gui("Terminating current dvisvgm process...")
                    self.dvisvgm_process.terminate()
                    # Give it a moment to terminate gracefully
                    try:
                        self.dvisvgm_process.wait(timeout=5)
                        self._log_message_gui("Process terminated gracefully")
                    except subprocess.TimeoutExpired:
                        self._log_message_gui("Process didn't terminate gracefully, forcing kill...")
                        self.dvisvgm_process.kill()
                        self.dvisvgm_process.wait()
                        self._log_message_gui("Process killed")
                except Exception as e:
                    self._log_message_gui(f"Error terminating process: {e}")
                finally:
                    self.dvisvgm_process = None

            self.conversion_running = False
            self._set_ui_state(running=False) # Update UI immediately
            # Temporarily show stopping text - this is safe
            self.btn_convert.config(text="Stopping...")
            self.master.after(500, self._check_thread_stopped)
        else:
            if self.conversion_running:
                self._log_message_gui("Stop requested, but thread seems inactive. Resetting UI.")
                self.conversion_running = False
                self._set_ui_state(running=False)
            else:
                self._log_message_gui("No active conversion to stop.")

    # --- _check_thread_stopped (Main Thread) ---
    # (Minor change to use direct logging)
    def _check_thread_stopped(self):
        """Checks if the thread has finished after a stop request."""
        if not self.conversion_running:
             if not self.conversion_thread or not self.conversion_thread.is_alive():
                  self._log_message_gui("Conversion stopped.") # Log directly
                  self._set_ui_state(running=False)
             else:
                  self._log_message_gui("Waiting for conversion thread to terminate...") # Log directly
                  self.master.after(500, self._check_thread_stopped) # Check again

    # --- _set_ui_state (Main Thread) ---
    # (No changes needed)
    def _set_ui_state(self, running: bool):
        state = tk.DISABLED if running else tk.NORMAL
        button_text = "Stop Conversion" if running else "Convert to SVG"
        try:
            if self.btn_convert.winfo_exists(): self.btn_convert.config(text=button_text, state=tk.NORMAL)
            if self.btn_add_files.winfo_exists(): self.btn_add_files.config(state=state)
            if self.btn_clear_list.winfo_exists(): self.btn_clear_list.config(state=state)
            if self.btn_browse_output.winfo_exists(): self.btn_browse_output.config(state=state)
        except tk.TclError: pass

    # --- Error Parsing Methods (Refactored for maintainability) ---
    def _parse_font_error(self, stderr_text, stderr_lower):
        """Parse font-related errors."""
        if "font" in stderr_lower and "not found" in stderr_lower:
            match = re.search(r"font\s+['\"]?([^'\"\s]+)['\"]?\s+not found", stderr_lower)
            font_name = match.group(1) if match else "unknown"
            return f"Missing Font: '{font_name}'. Ensure the required font is installed and accessible by dvisvgm/kpathsea."

        if "error" in stderr_lower and "loading font" in stderr_lower:
            return "Error loading a font. The font file might be corrupted or incompatible."

        return None

    def _parse_file_error(self, stderr_text, stderr_lower):
        """Parse file access related errors."""
        if "can't open file" in stderr_lower or "cannot open input file" in stderr_lower:
            match = re.search(r"(?:can't open file|cannot open input file)\s*['\"]?([^'\"\s]+)['\"]?", stderr_lower)
            fname = f" '{match.group(1)}'" if match else ""
            return f"Cannot open input file{fname}. Check if the file exists, is not corrupted, and has read permissions."

        if "i/o error" in stderr_lower:
            return "Input/Output error. Check file permissions, disk space, or if the file is locked."

        if "permission denied" in stderr_lower:
            return "Permission denied. Check read permissions for the input file and write permissions for the output folder."

        return None

    def _parse_format_error(self, stderr_text, stderr_lower):
        """Parse format-specific errors (PostScript, PDF, DVI)."""
        if any(keyword in stderr_lower for keyword in ["postscript error", "ghostscript", "ps interpreter failed"]):
            return "Error processing PostScript/EPS data. Ghostscript might be needed/misconfigured, or the PS/EPS file has issues."

        if "pdf parser error" in stderr_lower:
            return "Error parsing PDF file. The PDF might be corrupted, password-protected, or use unsupported features."

        if "error" in stderr_lower and "processing dvi specials" in stderr_lower:
            return "Error processing DVI specials. The DVI might reference external files incorrectly or use unsupported specials."

        return None

    def _parse_general_error(self, stderr_text, stderr_lower):
        """Parse general errors and extract error messages."""
        if "invalid argument" in stderr_lower:
            return "Invalid argument passed to dvisvgm (internal program error or bad command)."

        # Try to extract specific error messages
        match = re.search(r"^(?:error|fatal error):\s*(.*)", stderr_text, re.MULTILINE | re.IGNORECASE)
        if match:
            return f"General Error: {match.group(1).strip()}"

        return None

    def parse_dvisvgm_error(self, stderr_text):
        """
        Parse dvisvgm error messages using a structured approach.
        Returns a human-readable interpretation of the error.
        """
        if not stderr_text:
            return "No specific error message captured from dvisvgm."

        stderr_lower = stderr_text.lower()

        # Define error parsing methods in order of specificity
        error_parsers = [
            self._parse_font_error,
            self._parse_file_error,
            self._parse_format_error,
            self._parse_general_error,
        ]

        # Try each parser until one returns a result
        for parser in error_parsers:
            try:
                result = parser(stderr_text, stderr_lower)
                if result:
                    return result
            except Exception as e:
                # Log parsing errors but continue with other parsers
                print(f"Warning: Error in parser {parser.__name__}: {e}")
                continue

        # Fallback if no specific error pattern is matched
        return "Unknown error occurred during conversion. Check the raw output below."


    # --- Signal Handling and Closing (Mostly unchanged, direct logging ok in signal/close handlers if careful) ---
    def signal_handler(self, sig, frame):
        # Suppress unused parameter warnings
        _ = sig, frame
        print("\nCtrl+C detected, shutting down gracefully...")
        if self.master.winfo_exists():
             # Use _log_message_gui here if you want it in the window, but print might be safer during shutdown
             # self._log_message_gui("Ctrl+C detected, shutting down...") # Maybe omit this
             self.master.after(0, self.on_closing) # Schedule on_closing
        else: sys.exit(1)

    def on_closing(self):
        if self.conversion_running:
            if messagebox.askyesno("Confirm Exit", "Conversion is in progress. Are you sure you want to exit? The current file might not complete."):
                self.conversion_running = False
                self._log_message_gui("--- Exiting application - Attempting to stop conversion ---") # Direct log ok here
                self.master.after(200, self._check_before_destroy)
            else: return
        else: self.master.destroy()

    def _check_before_destroy(self):
        if self.conversion_thread and self.conversion_thread.is_alive():
            self._log_message_gui("Conversion thread still active. Forcing exit.") # Direct log ok here
        self.master.destroy()


# --- Main Execution ---
# (No changes needed here)
if __name__ == "__main__":
    root = tk.Tk()
    app = DviSvgConverterApp(root)

    if root.winfo_exists():
        try:
            while root.winfo_exists():
                root.update()
                root.update_idletasks()
        except tk.TclError as e:
             if "application has been destroyed" not in str(e): print(f"Tkinter TclError: {e}"); traceback.print_exc()
        except KeyboardInterrupt:
             print("\nCtrl+C detected in main loop, initiating shutdown...")
             if app and app.master.winfo_exists(): app.on_closing()
             elif root.winfo_exists(): root.destroy()
        except Exception as e:
            print(f"An unexpected error occurred in the main loop: {e}"); traceback.print_exc()
            if root.winfo_exists(): root.destroy()
            sys.exit(1)