#!/usr/bin/env python3
"""
Test script for DVISVGM GUI high priority fixes.
This script tests the queue processing, subprocess management, input validation, and error counting.
"""

import os
import sys
import tempfile
import time
import threading
import queue
import subprocess
from pathlib import Path

# Add the current directory to path to import the GUI module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_queue_processing():
    """Test that queue processing doesn't freeze with many items."""
    print("Testing queue processing with multiple rapid updates...")
    
    # Import here to avoid GUI initialization during import
    import tkinter as tk
    # Import the module with space in filename
    import importlib.util
    spec = importlib.util.spec_from_file_location("dvisvgm_gui", "DVISVGM GUI.py")
    dvisvgm_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(dvisvgm_module)
    DviSvgConverterApp = dvisvgm_module.DviSvgConverterApp
    
    root = tk.Tk()
    root.withdraw()  # Hide the main window for testing
    
    try:
        app = DviSvgConverterApp(root)
        
        # Add many items to the queue rapidly
        start_time = time.time()
        for i in range(100):
            app.gui_queue.put(('log', f"Test message {i}"))
        
        # Process queue items and measure time
        app.process_gui_queue()
        end_time = time.time()
        
        processing_time = end_time - start_time
        print(f"✓ Queue processing completed in {processing_time:.3f} seconds")
        
        # Verify queue is not completely empty (some items should remain due to limit)
        remaining_items = app.gui_queue.qsize()
        print(f"✓ Queue processing limit working: {remaining_items} items remaining")
        
        return True
        
    except Exception as e:
        print(f"✗ Queue processing test failed: {e}")
        return False
    finally:
        root.destroy()

def test_input_validation():
    """Test comprehensive input validation."""
    print("\nTesting input validation...")
    
    import tkinter as tk
    # Import the module with space in filename
    import importlib.util
    spec = importlib.util.spec_from_file_location("dvisvgm_gui", "DVISVGM GUI.py")
    dvisvgm_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(dvisvgm_module)
    DviSvgConverterApp = dvisvgm_module.DviSvgConverterApp
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        app = DviSvgConverterApp(root)
        
        # Test 1: Valid file validation
        with tempfile.NamedTemporaryFile(suffix='.dvi', delete=False) as temp_file:
            temp_file.write(b"dummy content")
            temp_path = temp_file.name
        
        try:
            is_valid, error_msg = app.validate_file_path(temp_path)
            if is_valid:
                print("✓ Valid file validation passed")
            else:
                print(f"✗ Valid file validation failed: {error_msg}")
                return False
        finally:
            os.unlink(temp_path)
        
        # Test 2: Non-existent file validation
        is_valid, error_msg = app.validate_file_path("nonexistent.dvi")
        if not is_valid and "does not exist" in error_msg:
            print("✓ Non-existent file validation passed")
        else:
            print("✗ Non-existent file validation failed")
            return False
        
        # Test 3: Invalid extension validation
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
            temp_file.write(b"dummy content")
            temp_path = temp_file.name
        
        try:
            is_valid, error_msg = app.validate_file_path(temp_path)
            if not is_valid and "extension" in error_msg.lower():
                print("✓ Invalid extension validation passed")
            else:
                print(f"✗ Invalid extension validation failed: {error_msg}")
                return False
        finally:
            os.unlink(temp_path)
        
        # Test 4: Output directory validation
        with tempfile.TemporaryDirectory() as temp_dir:
            is_valid, error_msg = app.validate_output_directory(temp_dir)
            if is_valid:
                print("✓ Valid output directory validation passed")
            else:
                print(f"✗ Valid output directory validation failed: {error_msg}")
                return False
        
        # Test 5: Non-existent directory validation
        is_valid, error_msg = app.validate_output_directory("nonexistent_directory")
        if not is_valid and "does not exist" in error_msg:
            print("✓ Non-existent directory validation passed")
        else:
            print("✗ Non-existent directory validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Input validation test failed: {e}")
        return False
    finally:
        root.destroy()

def test_subprocess_management():
    """Test subprocess management and termination."""
    print("\nTesting subprocess management...")
    
    # Test that we can create and terminate a process properly
    try:
        # Create a long-running process that we can terminate
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(['ping', '-t', 'localhost'], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE,
                                     creationflags=subprocess.CREATE_NO_WINDOW)
        else:  # Unix-like
            process = subprocess.Popen(['ping', 'localhost'], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
        
        # Let it run briefly
        time.sleep(0.5)
        
        # Terminate it
        process.terminate()
        try:
            process.wait(timeout=5)
            print("✓ Process terminated gracefully")
        except subprocess.TimeoutExpired:
            process.kill()
            process.wait()
            print("✓ Process killed after timeout")
        
        return True
        
    except Exception as e:
        print(f"✗ Subprocess management test failed: {e}")
        return False

def test_error_counting_logic():
    """Test that error counting logic is correct."""
    print("\nTesting error counting logic...")
    
    # This is a logical test of the counting algorithm
    # Simulate the corrected logic
    total_files = 10
    success_count = 3
    error_count = 2
    skipped_count = 0
    current_file_index = 5  # 0-based, so this is the 6th file
    
    # Simulate FileNotFoundError scenario (corrected logic)
    error_count += 1  # Count current file as error
    remaining_files = total_files - (current_file_index + 1)
    skipped_count += remaining_files
    
    # Verify the math
    expected_error_count = 3  # 2 previous + 1 current
    expected_skipped_count = 4  # 10 - 6 = 4 remaining files
    expected_total_processed = success_count + error_count + skipped_count
    
    if (error_count == expected_error_count and 
        skipped_count == expected_skipped_count and 
        expected_total_processed == total_files):
        print("✓ Error counting logic is mathematically correct")
        print(f"  Success: {success_count}, Errors: {error_count}, Skipped: {skipped_count}")
        return True
    else:
        print("✗ Error counting logic failed")
        print(f"  Expected - Success: {success_count}, Errors: {expected_error_count}, Skipped: {expected_skipped_count}")
        print(f"  Actual   - Success: {success_count}, Errors: {error_count}, Skipped: {skipped_count}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING HIGH PRIORITY FIXES FOR DVISVGM GUI")
    print("=" * 60)
    
    tests = [
        ("Queue Processing", test_queue_processing),
        ("Input Validation", test_input_validation),
        ("Subprocess Management", test_subprocess_management),
        ("Error Counting Logic", test_error_counting_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All high priority fixes are working correctly!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
